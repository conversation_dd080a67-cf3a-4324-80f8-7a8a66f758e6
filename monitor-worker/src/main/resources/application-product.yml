server:
  port: 8082
spring:
  datasource:
    dynamic:
      datasource:
        mysql:
          url: **************************************************************************************************************************************************************
          username: root
          password: jdlX2022
        postgresql:
          url: ************************************************************************************
          username: rover_map
          password: jdlX2022
  cloud:
    nacos:
      discovery:
        server-addr: http://jdxnacos.jd.local/
        username: nacos
        password: nacos@jdlX2022
  data:
    redis:
      host: redis-xvmv718wpfnw-proxy-nlb.jvessel-open-hb.jdcloud.com
      password: jdlX2022
  kafka:
    bootstrap-servers: broker-kafka-dzk69k556l-az1-0.jvessel-open-hb.jdcloud.com:9092,broker-kafka-dzk69k556l-az1-1.jvessel-open-hb.jdcloud.com:9092,broker-kafka-dzk69k556l-az1-2.jvessel-open-hb.jdcloud.com:9092
jmq:
  address: nameserver.jmq.jd.local:80
  password: c64bee7f154e4b95b596c715c6b02afe
  app: monitorworker
monitor-notify:
  baseUser: mahongping,gulin21,org.jishuzhici1,xiaojun34,anxin19,org.znjsbjszc1,bjwangzichen
  monitorUrl: https://jdxmonitor.jdl.cn/app/single?license=
jdd:
  easyjob:
    enable: true
    host: http://schedule.jdfin.local
    secret: 316dea8940776107c377a645a05c29c1
monitor:
  jmq:
    topic:
      transport-property-change: transport_property_change
      transport-events-change: transport_device_events
      intelligent-device-message: i_device_device_message
      transport-services-reply: transport_device_services_reply
      transport-device-property: transport_device_property
      metadata-integrate-station-change: metadata_integrate_station_message
      schedule-schedule-change: server_schedule_task
      schedule-order-change: server_schedule_monitor_order_task
      accident-flow-event: monitor_accident_flow_event
      vehicle-event-change: monitor_vehicle_change_event
      vehicle-map-alarm: map_vehicle_alarm
      vehicle-manual-alarm: monitor_manual_vehicle_alarm
      metadata-cockpit-team-change: metadata_cockpit_team_message
      metadata-cockpit-vehicle-change: metadata_cockpit_vehicle_message
      metadata-error-code-change: metadata_error_code_translate_change_message
      metadata-require-info-change: metadata_vehicle_require_info
      metadata-user-station-change: metadata_user_station_message
      metadata-user-stop-change: metadata_stop_message
      metadata-user-vehicle-change: operation_user_vehicle_change
      metadata-vehicle-basic-change: metadata_vehicle_message
      metadata-vehicle-exception-change: metadata_vehicle_exception_message
      metadata-device-state-change: metadata_device_status_message
      metadata-wh-work-mode-message: metadata_wh_work_mode_message
      ticket-issue-change: issue_full_info_sync
      ticket-issue-cockpit-change: issue_cockpit_list
      ticket-issue-today-statistic: issue_today_statistic
      server-guardian-alarm: server_guardian_alarm
      server-guardian-connect: server_guardian_connect
      server-guardian-pnc-info: server_guardian_pnc
      server-guardian-power: server_guardian_power
      server-local-view: server_decrease_local_view
      server-mqtt-topic: mqtt_system_disconnected
      server-pdu-will: r_pdu_will
      server-drive-will: r_drive_will
      server-power-manager: server_power_manager
      server-report-abnormal: server_report_abnormal
      server-report-boot: server_report_boot
      server-hardware-event: server_report_hardware_event
      vehicle-status-change: server_vehicle_change_status
      shadow-video-task-result: rover_shadow_event_task_result
      map-variable-upgrade: map_variable_version_upgrade
      monitor_remote_control_command: monitor_remote_control_command
      monitor_remote_command_log: monitor_remote_command_log
      reply_r_drive_control_connect_server: reply_r_drive_control_connect_server
      monitor_map_collection_takeover: monitor_map_collection_takeover
      schedule-pnc-route: server_schedule_pnc_route
      monitor_robot_change_event: monitor_robot_change_event
      integrate_task_info_message: integrate_task_info_message
      amr_congestion_info_message: amr_congestion_info_message
      metadata_map_route_message: metadata_map_route_message
    provider:
      topic:
        monitor_cockpit_realtime_status_change: cockpit_realtime_status_change
        monitor_remote_command_log: monitor_remote_command_log
        shadow_tracking_event: rover_shadow_tracking_event
        shadow_jira_event: rover_shadow_jira_event
        monitor_pnc_route: monitor_pnc_route
        server_web_terminal_command_down: server_web_terminal_command_down
        monitor_vehicle_remote_command_operation: vehicle_remote_command_operation
        monitor_remote_control_command: monitor_remote_control_command
        monitor_manual_vehicle_alarm: monitor_manual_vehicle_alarm
        monitor_view_order_detail: monitor_view_order_detail
        monitor_vehicle_change_event: monitor_vehicle_change_event
        monitor_single_vehicle_realtime: monitor_single_vehicle_realtime
        monitor_single_vehicle_schedule: monitor_single_vehicle_schedule
        monitor_accident_flow_event: monitor_accident_flow_event
        map_vehicle_alarm: map_vehicle_alarm
        monitor_robot_change_event: monitor_robot_change_event

rover-video:
  snapshotUrl: http://rover-video-process.jd.local/video/process/snapshot/realtime
  uid: jdx
  key: cb0f1cf005d18ad1757f1a739ace63b5c2f2a449
accident:
  url: https://jdxmonitor.jdl.cn/app/accidentmanage
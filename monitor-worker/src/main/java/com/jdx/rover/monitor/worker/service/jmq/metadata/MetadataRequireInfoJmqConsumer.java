/**
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq.metadata;

import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.metadata.api.domain.message.RequireInfoMessage;
import com.jdx.rover.metadata.domain.dto.require.RequireInfoDTO;
import com.jdx.rover.monitor.enums.mobile.MessageModuleEnum;
import com.jdx.rover.monitor.enums.mobile.MessageStatusEnum;
import com.jdx.rover.monitor.enums.mobile.MessageTypeEnum;
import com.jdx.rover.monitor.manager.require.RequireInfoManager;
import com.jdx.rover.monitor.manager.todo.UserTodoTaskManager;
import com.jdx.rover.monitor.manager.user.MetadataUserApiManager;
import com.jdx.rover.monitor.po.UserTodoTask;
import com.jdx.rover.permission.domain.dto.basic.UserInfoDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 监听维修单变更信息
 *
 * <AUTHOR>
 * @date 2024/12/10
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class MetadataRequireInfoJmqConsumer implements MessageListener {

    private final UserTodoTaskManager userTodoTaskManager;

    private final MetadataUserApiManager metadataUserApiManager;

    private final RequireInfoManager requireInfoManager;

    /**
     * 处理来自消息队列的消息。
     * @param messages 接收到的消息列表。
     */
    @Override
    public void onMessage(List<Message> messages) throws Exception {
        for (Message message : messages) {
            log.info("Received topic={}, message={}", message.getTopic(), message.getText());
            if (StringUtils.isBlank(message.getText())) {
                continue;
            }
            try {
                handleOneMessage(message.getText());
            } catch (Exception e) {
                log.error("处理消息失败!{}", message.getText(), e);
            }
        }
    }

    /**
     * 处理单条消息，根据消息内容更新或创建用户待办任务。
     */
    private void handleOneMessage(String message) {
        RequireInfoMessage requireInfoMessage = JsonUtils.readValue(message, RequireInfoMessage.class);
        if (requireInfoMessage == null || StringUtils.isBlank(requireInfoMessage.getRequireNumber())) {
            log.error("RequireInfoMessage or requireNumber absent!");
            return;
        }
        if (requireInfoMessage.getRequireStatus() != 3 && requireInfoMessage.getRequireStatus() != 5 && requireInfoMessage.getRequireStatus() != 6) {
            return;
        }
        UserTodoTask userTodoTask = userTodoTaskManager.lambdaQuery().eq(UserTodoTask::getBusinessKey, requireInfoMessage.getRequireNumber()).one();
        if (userTodoTask != null) {
            //维修状态变为完成,变更卡片状态为完成
            if (requireInfoMessage.getRequireStatus() == 6) {
                userTodoTaskManager.lambdaUpdate().eq(UserTodoTask::getId, userTodoTask.getId()).set(UserTodoTask::getStatus, MessageStatusEnum.FINISH.getValue()).update();
            } else if (requireInfoMessage.getRequireStatus() == 5) {
                userTodoTaskManager.lambdaUpdate().eq(UserTodoTask::getId, userTodoTask.getId()).set(UserTodoTask::getStatus, MessageStatusEnum.HANDLE.getValue()).update();
            }
            return ;
        }
        UserTodoTask task = new UserTodoTask();
        task.setModule(MessageModuleEnum.REPAIR.getValue());
        task.setStatus(MessageStatusEnum.HANDLE.getValue());
        //获取维修单详情
        RequireInfoDTO requireInfo = requireInfoManager.getRequireInfo(requireInfoMessage.getRequireNumber());
        task.setVehicleName(requireInfo.getVehicleName());
        task.setStationName(requireInfo.getStationName());
        task.setPushTime(new Date());
        UserInfoDTO userInfoDTO = metadataUserApiManager.getUserInfoByJdErp(requireInfo.getReportErp());
        String userName = userInfoDTO == null ? requireInfo.getReportErp() : userInfoDTO.getUserName();
        task.setOwner(userName);
        task.setCreateUser(userName);
        task.setModifyUser(userName);
        task.setBusinessKey(requireInfoMessage.getRequireNumber());
        if (requireInfoMessage.getRequireStatus() == 3) {
            task.setType(MessageTypeEnum.REPAIR_REJECT.getValue());
            task.setDescription("车辆维修申请驳回，请确认");
        } else if (requireInfoMessage.getRequireStatus() == 5) {
            task.setType(MessageTypeEnum.REPAIR_CONFIRM.getValue());
            task.setDescription("车辆维修完成，请确认车辆可用");
        }
        userTodoTaskManager.save(task);
    }
}

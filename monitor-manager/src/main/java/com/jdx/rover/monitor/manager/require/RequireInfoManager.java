package com.jdx.rover.monitor.manager.require;

import com.jdx.rover.common.utils.exception.AppException;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.metadata.domain.dto.require.RequireDetailDTO;
import com.jdx.rover.metadata.domain.dto.require.RequireInfoDTO;
import com.jdx.rover.metadata.domain.vo.require.CheckRejectRequireVO;
import com.jdx.rover.metadata.domain.vo.require.CheckRequireVO;
import com.jdx.rover.metadata.jsf.service.require.MetadataRequireJsfService;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @description: CockpitDataManager
 * @author: wangguotai
 * @create: 2024-06-20 09:22
 **/
@Slf4j
@RequiredArgsConstructor
@Service
public class RequireInfoManager {

    private final MetadataRequireJsfService metadataRequireJsfService;

    /**
     * 获取维修单详情
     *
     * @param requireNumber requireNumber
     * @return RequireDetailDTO
     */
    public RequireDetailDTO getRequireDetail(String requireNumber) {
        try {
            log.info("metadataRequireClient call getRequireDetail request:[{}].", requireNumber);
            HttpResult<RequireDetailDTO> httpResult = metadataRequireJsfService.getRequireDetail(requireNumber);
            log.info("metadataRequireClient call getRequireDetail response:[{}].", httpResult);
            if (HttpResult.isSuccess(httpResult)) {
                return httpResult.getData();
            } else {
                throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
            }
        } catch (Exception e) {
            log.error("metadataRequireClient call getRequireDetail exception.", e);
            throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
        }
    }


    /**
     * 处理
     * @param checkRequireVO
     * @return
     */
    public Boolean checkRequire(CheckRequireVO checkRequireVO) {
        try {
            log.info("metadataRequireClient call checkRequire request:[{}].", checkRequireVO);
            HttpResult<Object> httpResult = metadataRequireJsfService.checkRequire(checkRequireVO);
            log.info("metadataRequireClient call getRequireDetail response:[{}].", httpResult);
            if (HttpResult.isSuccess(httpResult)) {
                return true;
            } else {
                throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
            }
        } catch (Exception e) {
            log.error("metadataRequireClient call getRequireDetail exception.", e);
        }
        return false;
    }

    public RequireInfoDTO getRequireInfo(String requireNumber) {
        try {
            log.info("metadataRequireClient call getRequireInfo request:[{}].", requireNumber);
            HttpResult<RequireInfoDTO> httpResult = metadataRequireJsfService.getRequireInfo(requireNumber);
            log.info("metadataRequireClient call getRequireInfo response:[{}].", httpResult);
            if (HttpResult.isSuccess(httpResult)) {
                return httpResult.getData();
            } else {
                throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
            }
        } catch (Exception e) {
            log.error("metadataRequireClient call getRequireInfo exception.", e);
            throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
        }
    }

    /**
     * 处理
     * @param checkRejectRequireVO
     * @return
     */
    public Boolean checkRejectRequire(CheckRejectRequireVO checkRejectRequireVO) {
        try {
            log.info("metadataRequireClient call checkRejectRequire request:[{}].", checkRejectRequireVO);
            HttpResult<Object> httpResult = metadataRequireJsfService.checkRejectRequire(checkRejectRequireVO);
            log.info("metadataRequireClient call checkRejectRequire response:[{}].", httpResult);
            if (HttpResult.isSuccess(httpResult)) {
                return true;
            } else {
                throw new AppException(MonitorErrorEnum.ERROR_CALL_SERVICE.getCode(), MonitorErrorEnum.ERROR_CALL_SERVICE.getMessage());
            }
        } catch (Exception e) {
            log.error("metadataRequireClient call checkRejectRequire exception.", e);
        }
        return false;
    }
}
/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.service.robot;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.core.type.TypeReference;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.device.jsfapi.domain.dto.group.GroupInfoDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.PdaStatusStatisticInfoDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.robot.RobotDeviceAbnormalInfoDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.robot.RobotDeviceAbnormalListDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.robot.RobotDeviceBasicDetailInfoDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.robot.RobotDeviceGroupInfoDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.robot.RobotDevicePageSearchDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.robot.RobotDeviceRealtimePageInfoDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.robot.RobotDeviceRealtimeStateDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.robot.RobotTreeDeviceRealtimeInfoDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.dto.robot.RobotTreeGroupBasicInfoDTO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.robot.RobotDeviceAbnormalSearchVO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.robot.RobotDevicePageSearchVO;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.robot.RobotProductSearchVO;
import com.jdx.rover.monitor.dto.robot.RobotDeviceRealtimeInfoDTO;
import com.jdx.rover.monitor.dto.robot.RobotGroupMapInfoDTO;
import com.jdx.rover.monitor.dto.robot.RobotMapRealtimeInfoDTO;
import com.jdx.rover.monitor.entity.VehicleTakeOverEntity;
import com.jdx.rover.monitor.entity.alarm.RobotAlarmDO;
import com.jdx.rover.monitor.entity.alarm.RobotAlarmDO.DeviceAlarmEventDO;
import com.jdx.rover.monitor.entity.device.RobotIssueDO;
import com.jdx.rover.monitor.entity.device.RobotScheduleDO;
import com.jdx.rover.monitor.enums.AlarmLevelEnum;
import com.jdx.rover.monitor.enums.device.*;
import com.jdx.rover.monitor.manager.device.IntelligentDeviceApiManager;
import com.jdx.rover.monitor.manager.device.IntelligentDeviceGroupApiManager;
import com.jdx.rover.monitor.manager.device.TransportDeviceApiManager;
import com.jdx.rover.monitor.manager.robot.RobotAbnormalInfoManager;
import com.jdx.rover.monitor.manager.robot.RobotRealtimeInfoManager;
import com.jdx.rover.monitor.po.robot.RobotAbnormalInfo;
import com.jdx.rover.monitor.po.robot.RobotRealtimeInfo;
import com.jdx.rover.monitor.repository.mapper.RobotRealtimeInfoMapper;
import com.jdx.rover.monitor.repository.redis.VehicleTakeOverRepository;
import com.jdx.rover.monitor.repository.redis.robot.RobotAlarmRepository;
import com.jdx.rover.monitor.repository.redis.robot.RobotIssueRepository;
import com.jdx.rover.monitor.repository.redis.robot.RobotScheduleRepository;
import com.jdx.rover.monitor.repository.util.PageUtils;
import com.jdx.rover.monitor.vo.robot.RobotCommandBaseVO;
import com.jdx.rover.monitor.vo.robot.RobotMapInfoRequestVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 物联网设备机器人服务
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MonitorRobotService {

    /**
     * 用于访问操作实时状态实体的Mapper接口
     */
    public final RobotRealtimeInfoManager robotRealtimeInfoManager;

    /**
     * 用于访问操作实时状态实体的Mapper接口
     */
    public final RobotRealtimeInfoMapper robotRealtimeInfoMapper;

    /**
     * 用于访问产品分组接口
     */
    public final IntelligentDeviceGroupApiManager deviceGroupApiManager;

    /**
     * 设备服务接口
     * 获取指定设备的基本信息
     */
    public final IntelligentDeviceApiManager basicDeviceApiManager;

    /**
     * 设备状态服务接口
     * 获取指定设备的实时信息
     */
    public final TransportDeviceApiManager transportDeviceApiManager;

    /**
     * 用于处理机器人地图相关服务操作的接口
     */
    private final RobotMapService robotMapService;

    /**
     * 用于访问异常实体的Mapper接口
     */
    public final RobotAbnormalInfoManager robotAbnormalInfoManager;

    /**
     * 用于访问实时告警的Repo接口
     */
    public final RobotAlarmRepository robotAlarmRepository;

    /**
     * 用于访问实时工单的Repo接口
     */
    public final RobotIssueRepository robotIssueRepository;

    /**
     * 用于访问实时业务的Repo接口
     */
    public final RobotScheduleRepository robotScheduleRepository;

    /**
     * 接管缓存
     */
    public final VehicleTakeOverRepository vehicleTakeOverRepository;

    /**
     * 获取机器人设备树。
     */
    public List<RobotTreeGroupBasicInfoDTO> getGroupTree(RobotProductSearchVO productSearchVo) {
        List<GroupInfoDTO> result = deviceGroupApiManager.getGroupTree(productSearchVo.getProductKey());
        List<RobotTreeGroupBasicInfoDTO> groupInfoDtoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(result)) {
            log.error("查询设备分组信息不存在");
            return groupInfoDtoList;
        }
        // 查询设备列表
        LambdaQueryWrapper<RobotRealtimeInfo> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(RobotRealtimeInfo::getProductKey, productSearchVo.getProductKey());
        queryWrapper.in(CollectionUtils.isNotEmpty(productSearchVo.getWorkMode()), RobotRealtimeInfo::getWorkMode, productSearchVo.getWorkMode());
        queryWrapper.select(RobotRealtimeInfo::getGroupOne, RobotRealtimeInfo::getGroupTwo, RobotRealtimeInfo::getGroupThree, RobotRealtimeInfo::getDeviceName, RobotRealtimeInfo::getRemarkName, RobotRealtimeInfo::getRealtimeStatus);
        List<RobotRealtimeInfo> robotRealtimeInfos = robotRealtimeInfoManager.list(queryWrapper);
        log.info("获取设备存储库结果{}", JsonUtils.writeValueAsString(robotRealtimeInfos));
        // 构建树形结构
        Map<String, List<RobotRealtimeInfo>> deviceMap = robotRealtimeInfos.stream().collect(Collectors.groupingBy(RobotRealtimeInfo::getGroupOne));
        for (GroupInfoDTO groupInfoDto : result) {
            RobotTreeGroupBasicInfoDTO basicInfoDto = new RobotTreeGroupBasicInfoDTO();
            basicInfoDto.setGroupNo(groupInfoDto.getGroupNo());
            basicInfoDto.setGroupName(groupInfoDto.getName());
            AtomicInteger onLineCount = new AtomicInteger(0);
            if (Objects.nonNull(deviceMap) && Objects.nonNull(deviceMap.get(groupInfoDto.getGroupNo()))) {
                    List<RobotTreeDeviceRealtimeInfoDTO> deviceDtoList = deviceMap.get(groupInfoDto.getGroupNo()).stream().filter(device ->
                            StringUtils.isAllBlank(device.getGroupTwo(), device.getGroupThree())).map(device -> {
                        RobotTreeDeviceRealtimeInfoDTO realtimeInfoDto = new RobotTreeDeviceRealtimeInfoDTO();
                        realtimeInfoDto.setDeviceName(device.getDeviceName());
                        realtimeInfoDto.setRemarkName(device.getRemarkName());
                        realtimeInfoDto.setRealtimeStatus(device.getRealtimeStatus());
                        if (!StringUtils.equals(device.getRealtimeStatus(), DeviceRealtimeStateEnum.OFFLINE.getValue())) {
                            onLineCount.incrementAndGet();
                        }
                        return realtimeInfoDto;
                    }).collect(Collectors.toList());
                    basicInfoDto.setDeviceList(deviceDtoList);
                basicInfoDto.setTotalCount(deviceDtoList.size());
                basicInfoDto.setOnlineCount(onLineCount.intValue());
            }
            if (CollectionUtils.isNotEmpty(groupInfoDto.getChildren())) {
                List<RobotTreeGroupBasicInfoDTO> childList = buildGroupChild(groupInfoDto.getGroupNo(), groupInfoDto.getChildren(), robotRealtimeInfos);
                basicInfoDto.setChild(childList);
                Integer totalCount = childList.stream().mapToInt(RobotTreeGroupBasicInfoDTO::getTotalCount).sum();
                Integer onlineCount = childList.stream().mapToInt(RobotTreeGroupBasicInfoDTO::getOnlineCount).sum();
                basicInfoDto.setTotalCount(basicInfoDto.getTotalCount() + totalCount);
                basicInfoDto.setOnlineCount(basicInfoDto.getOnlineCount() + onlineCount);
            }
            groupInfoDtoList.add(basicInfoDto);
        }
        return groupInfoDtoList;
    }


    /**
     * 根据条件分页查询机器人设备列表
     * @param devicePageSearchVo 查询条件对象
     * @return 机器人设备分页查询结果DTO
     */
    public RobotDevicePageSearchDTO pageSearchList(RobotDevicePageSearchVO devicePageSearchVo) {
        // 获取用户权限下车辆
        RobotDevicePageSearchDTO pageInfo = new RobotDevicePageSearchDTO();
        LambdaQueryWrapper<RobotRealtimeInfo> queryWrapper = new LambdaQueryWrapper<RobotRealtimeInfo>();
        queryWrapper.eq(StringUtils.isNotEmpty(devicePageSearchVo.getGroupOne()), RobotRealtimeInfo::getGroupOne, devicePageSearchVo.getGroupOne());
        queryWrapper.eq(StringUtils.isNotEmpty(devicePageSearchVo.getGroupTwo()), RobotRealtimeInfo::getGroupTwo, devicePageSearchVo.getGroupTwo());
        queryWrapper.eq(StringUtils.isNotEmpty(devicePageSearchVo.getGroupThree()), RobotRealtimeInfo::getGroupThree, devicePageSearchVo.getGroupThree());
        queryWrapper.eq(StringUtils.isNotEmpty(devicePageSearchVo.getProductKey()), RobotRealtimeInfo::getProductKey, devicePageSearchVo.getProductKey());
        queryWrapper.in(CollectionUtils.isNotEmpty(devicePageSearchVo.getWorkMode()), RobotRealtimeInfo::getWorkMode, devicePageSearchVo.getWorkMode());
        List<PdaStatusStatisticInfoDTO> statusGroupStatistic = robotRealtimeInfoManager.getGroupCountByStatus(queryWrapper);
        Map<String, Long> statisticMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(statusGroupStatistic)) {
            statisticMap =
                    statusGroupStatistic.stream().collect(Collectors.toMap(PdaStatusStatisticInfoDTO::getParam, PdaStatusStatisticInfoDTO::getCount));
        }
        Long onlineCount = statisticMap.get(DeviceRealtimeStateEnum.ONLINE.getValue());
        Long offlineCount = statisticMap.get(DeviceRealtimeStateEnum.OFFLINE.getValue());
        Long abnormalCount = statisticMap.get(DeviceRealtimeStateEnum.ABNORMAL.getValue());
        pageInfo.setOnlineCount(onlineCount == null? 0:onlineCount);
        pageInfo.setOfflineCount(offlineCount == null? 0:offlineCount);
        pageInfo.setAbnormalCount(abnormalCount == null? 0: abnormalCount);
        pageInfo.setTotalCount(pageInfo.getOnlineCount() + pageInfo.getOfflineCount()+ pageInfo.getAbnormalCount());
        queryWrapper.eq(StringUtils.isNotEmpty(devicePageSearchVo.getRealtimeStatus()), RobotRealtimeInfo::getRealtimeStatus, devicePageSearchVo.getRealtimeStatus());
        queryWrapper.orderByDesc(RobotRealtimeInfo::getModifiedTime);
        queryWrapper.select(RobotRealtimeInfo::getDeviceName, RobotRealtimeInfo::getProductKey, RobotRealtimeInfo::getRemarkName, RobotRealtimeInfo::getGroupName,  RobotRealtimeInfo::getGroupLevelName, RobotRealtimeInfo::getRealtimeStatus,
                RobotRealtimeInfo::getProductModelNo, RobotRealtimeInfo::getProductModelName, RobotRealtimeInfo::getWorkMode, RobotRealtimeInfo::getModifiedTime);
        IPage<RobotRealtimeInfo> iPage = PageUtils.toMpPage(devicePageSearchVo,RobotRealtimeInfo.class);
        IPage<RobotRealtimeInfo> robotRealtimeInfoIPage = robotRealtimeInfoManager.page(iPage, queryWrapper);
        pageInfo.setTotal(robotRealtimeInfoIPage.getTotal());
        pageInfo.setPageNum((int)robotRealtimeInfoIPage.getCurrent());
        pageInfo.setPageSize((int)robotRealtimeInfoIPage.getSize());
        if(CollectionUtils.isEmpty(robotRealtimeInfoIPage.getRecords())) {
            return pageInfo;
        }
        pageInfo.setList(buildRobotRealtimeInfo(robotRealtimeInfoIPage.getRecords()));
        return pageInfo;
    }

    /**
     * 获取指定产品和设备的基本信息。
     */
    public RobotDeviceBasicDetailInfoDTO getDeviceBasicInfo(String productKey, String deviceName) {
        // 查询设备列表
        RobotDeviceBasicDetailInfoDTO detailInfoDto = new RobotDeviceBasicDetailInfoDTO();
        RobotRealtimeInfo deviceDetailDto = robotRealtimeInfoManager.getDeviceNyName(productKey, deviceName);
        if (Objects.isNull(deviceDetailDto)) {
            return detailInfoDto;
        }
        detailInfoDto.setDeviceNo(deviceDetailDto.getDeviceName());
        detailInfoDto.setDeviceName(deviceDetailDto.getDeviceName());
        RobotDeviceGroupInfoDTO groupInfo = new RobotDeviceGroupInfoDTO();
        groupInfo.setGroupLevelName(deviceDetailDto.getGroupLevelName());
        if (StringUtils.equals(ProductTypeEnum.INTEGRATE.getValue(), productKey)) {
            groupInfo.setGroupNo(deviceDetailDto.getGroupOne());
        } else {
            groupInfo.setGroupNo(deviceDetailDto.getGroupThree());
        }
        detailInfoDto.setGroupInfo(groupInfo);
        detailInfoDto.setProductKey(deviceDetailDto.getProductKey());
        detailInfoDto.setProductModelName(deviceDetailDto.getProductModelName());
        detailInfoDto.setRemarkName(deviceDetailDto.getRemarkName());
        RobotDeviceRealtimeInfoDTO realtimeStateDto = new RobotDeviceRealtimeInfoDTO();
        Map<String, Object> realtimeMap = transportDeviceApiManager.getDeviceStatusDetail(deviceName, DevicePropertyCodeEnum.MONITOR_ROBOT_VERSION.getPropertyList());
        BeanUtil.fillBeanWithMapIgnoreCase(realtimeMap, realtimeStateDto, true);
        detailInfoDto.setMapId(realtimeStateDto.getMapId());
        detailInfoDto.setMapVersion(realtimeStateDto.getMapVersion());
        detailInfoDto.setRoverVersion(realtimeStateDto.getVersion());
        if (StringUtils.equals(productKey, ProductTypeEnum.INSPECTOR.getValue())) {
            RobotGroupMapInfoDTO groupMapInfoDto = robotMapService.getMapInfoByGroupNo(productKey, deviceDetailDto.getGroupThree());
            detailInfoDto.setMapId(groupMapInfoDto.getMapId());
            detailInfoDto.setMapVersion(groupMapInfoDto.getMapVersion());
        }
        return detailInfoDto;
    }

    /**
     * 获取指定产品和设备的实时状态。
     */
    public RobotDeviceRealtimeStateDTO getRealtimeStatus(String productKey, String deviceName) {
        RobotDeviceRealtimeStateDTO result = new RobotDeviceRealtimeStateDTO();
        LambdaQueryWrapper<RobotRealtimeInfo> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(RobotRealtimeInfo::getProductKey, productKey);
        lambdaQueryWrapper.eq(RobotRealtimeInfo::getDeviceName, deviceName);
        List<RobotRealtimeInfo> resultList = robotRealtimeInfoManager.list(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(resultList)) {
            return result;
        }
        RobotRealtimeInfo robotRealtimeInfo = resultList.get(0);
        result.setProductKey(productKey);
        result.setDeviceNo(robotRealtimeInfo.getRemarkName());
        result.setDeviceName(deviceName);
        if (StringUtils.isNotBlank(robotRealtimeInfo.getWorkMode())) {
            Optional.ofNullable(DeviceWorkModeEnum.of(robotRealtimeInfo.getWorkMode())).
                    ifPresent(data -> result.setWorkModeName(data.getWorkModeName()));
        }
        RobotScheduleDO robotScheduleDo = robotScheduleRepository.get(deviceName);
        if (Objects.nonNull(robotScheduleDo)) {
            result.setShelfTypeName(robotScheduleDo.getShelfTypeName());
            result.setShelfNo(robotScheduleDo.getShelfNo());
        }
        RobotDeviceRealtimeInfoDTO realtimeStateDto = new RobotDeviceRealtimeInfoDTO();
        Map<String, Object> realtimeMap = transportDeviceApiManager.getDeviceStatusDetail(deviceName, DevicePropertyCodeEnum.ALL_STATUS.getPropertyList());
        BeanUtil.fillBeanWithMapIgnoreCase(realtimeMap, realtimeStateDto, true);
        if (Objects.nonNull(realtimeStateDto.getBattery())) {
            result.setPower(Double.valueOf(realtimeStateDto.getBattery()));
        }
        if (StringUtils.equals(productKey, ProductTypeEnum.INSPECTOR.getValue())) {
            result.setTaskType(InspectTaskTypeEnum.getByValue(realtimeStateDto.getTaskType()).getName());
        }
        VehicleTakeOverEntity takeOverEntity = vehicleTakeOverRepository.get(deviceName);
        if (Objects.nonNull(takeOverEntity)) {
            result.setTakeOverUser(takeOverEntity.getUserName());
        }
        result.setChargingState(Objects.equals(realtimeStateDto.getCharging(), 1));
        result.setX(realtimeStateDto.getX());
        result.setY(realtimeStateDto.getY());
        result.setYaw(realtimeStateDto.getYaw());
        result.setEmergencyState(Objects.equals(realtimeStateDto.getEmergencyState(), 1));
        result.setSpeed(Optional.ofNullable(realtimeStateDto.getLinear()).map(value ->
                new BigDecimal(value).setScale(3,BigDecimal.ROUND_HALF_UP).doubleValue()).orElse(0.0));
        result.setMotorEnabled(Objects.equals(realtimeStateDto.getMotorEnabled(), 1));
        result.setSceneSignal(realtimeStateDto.getLocateConfidence());
        result.setRealtimeStatus(robotRealtimeInfo.getRealtimeStatus());
        result.setRealtimeStatusName(DeviceRealtimeStateEnum.getNameByValue(robotRealtimeInfo.getRealtimeStatus()));
        result.setTakeOverState(realtimeStateDto.getTakeOverState());
        return result;
    }

    /**
     * 获取设备异常信息列表。
     */
    public List<RobotDeviceAbnormalInfoDTO> getDeviceAbnormalInfo(RobotDeviceAbnormalSearchVO deviceAbnormalSearchVo) {
        LambdaQueryWrapper<RobotAbnormalInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(deviceAbnormalSearchVo.getDeviceName()), RobotAbnormalInfo::getDeviceName, deviceAbnormalSearchVo.getDeviceName());
        queryWrapper.eq(StringUtils.isNotBlank(deviceAbnormalSearchVo.getProductKey()), RobotAbnormalInfo::getProductKey, deviceAbnormalSearchVo.getProductKey());
        queryWrapper.eq(StringUtils.isNotBlank(deviceAbnormalSearchVo.getBootId()), RobotAbnormalInfo::getBootId, deviceAbnormalSearchVo.getBootId());
        queryWrapper.eq(StringUtils.isNotBlank(deviceAbnormalSearchVo.getAbnormalSource()), RobotAbnormalInfo::getPerformanceModule, deviceAbnormalSearchVo.getAbnormalSource());
        queryWrapper.isNull(RobotAbnormalInfo::getEndTime);
        List<RobotAbnormalInfo> robotAbnormalInfoList = robotAbnormalInfoManager.list(queryWrapper);
        return robotAbnormalInfoList.stream().filter(abnormalInfo -> Objects.isNull(abnormalInfo.getEndTime())).map(abnormalInfo -> {
            RobotDeviceAbnormalInfoDTO abnormalInfoDto = new RobotDeviceAbnormalInfoDTO();
            abnormalInfoDto.setAlarmCode(abnormalInfo.getErrorCode());
            DeviceAlarmCodeEnum alarmCodeEnum = DeviceAlarmCodeEnum.of(abnormalInfo.getErrorCode());
            if (Objects.nonNull(alarmCodeEnum)) {
                abnormalInfoDto.setAlarmCodeName(alarmCodeEnum.getAlarmMsg());
            }
            abnormalInfoDto.setAlarmLevel(abnormalInfo.getErrorLevel());
            AlarmLevelEnum alarmLevelEnum = AlarmLevelEnum.valueOf(abnormalInfo.getErrorLevel());
            if (Objects.nonNull(alarmLevelEnum)) {
                abnormalInfoDto.setAlarmLevelName(alarmLevelEnum.getName());
            }
            abnormalInfoDto.setEarliestReportTime(abnormalInfo.getStartTime());
            abnormalInfoDto.setLatestReportTime(abnormalInfo.getStartTime());
            abnormalInfoDto.setFollowUser(abnormalInfo.getFollowUser());
            return abnormalInfoDto;
        }).collect(Collectors.toList());
    }

    /**
     * 获取指定产品和设备的实时地图模式状态。
     */
    public List<RobotMapRealtimeInfoDTO> getMapRobotRealtimeInfo(RobotMapInfoRequestVO robotMapInfoRequestVo) {
        LambdaQueryWrapper<RobotRealtimeInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(RobotRealtimeInfo::getProductKey, robotMapInfoRequestVo.getProductKey());
        lambdaQueryWrapper.eq(StringUtils.isNotBlank(robotMapInfoRequestVo.getGroupOne()), RobotRealtimeInfo::getGroupOne, robotMapInfoRequestVo.getGroupOne());
        lambdaQueryWrapper.eq(StringUtils.isNotBlank(robotMapInfoRequestVo.getDeviceName()), RobotRealtimeInfo::getDeviceName, robotMapInfoRequestVo.getDeviceName());
        lambdaQueryWrapper.in(CollectionUtils.isNotEmpty(robotMapInfoRequestVo.getWorkMode()), RobotRealtimeInfo::getWorkMode, robotMapInfoRequestVo.getWorkMode());
        // 这里必填的
        lambdaQueryWrapper.select(RobotRealtimeInfo::getDeviceName, RobotRealtimeInfo::getRemarkName, RobotRealtimeInfo::getGroupName,  RobotRealtimeInfo::getGroupLevelName, RobotRealtimeInfo::getRealtimeStatus,
         RobotRealtimeInfo::getWorkMode, RobotRealtimeInfo::getModifiedTime);
        List<RobotRealtimeInfo> resultList = robotRealtimeInfoMapper.selectList(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(resultList)) {
            return new ArrayList<>();
        }
        log.info("获取指定产品和设备的实时地图模式状态，结果：{}", JsonUtils.writeValueAsString(resultList));
        Map<String, RobotRealtimeInfo> deviceListMap =
                resultList.stream().collect(Collectors.toMap(RobotRealtimeInfo::getDeviceName, Function.identity()));
        List<String> deviceList = resultList.stream().map(RobotRealtimeInfo::getDeviceName).collect(Collectors.toList());
        List<Map<String, Object>> mapStatusResult = transportDeviceApiManager.getDeviceStatusList(deviceList, DevicePropertyCodeEnum.MONITOR_ROBOT_STATUS.getPropertyList());
        Map<String, List<RobotAlarmDO.DeviceAlarmEventDO>> alarmMap = robotAlarmRepository.listMap(deviceList);
        if (CollectionUtils.isNotEmpty(mapStatusResult)) {
            return mapStatusResult.stream().map(data -> {
                RobotMapRealtimeInfoDTO realtimeStateDto = new RobotMapRealtimeInfoDTO();
                BeanUtil.fillBeanWithMapIgnoreCase(data, realtimeStateDto, true);
                RobotRealtimeInfo realtimeInfo = deviceListMap.get(realtimeStateDto.getDeviceName());
                if (Objects.nonNull(realtimeInfo)) {
                    realtimeStateDto.setProductKey(realtimeInfo.getProductKey());
                    realtimeStateDto.setRemarkName(realtimeInfo.getRemarkName());
                }
                realtimeStateDto.setAlarmState(DeviceAlarmCategoryEnum.NORMAL.getValue());
                List<RobotAlarmDO.DeviceAlarmEventDO> alarmDo = alarmMap.get(realtimeStateDto.getDeviceName());
                log.info("获取设备告警信息{}", JsonUtils.writeValueAsString(alarmDo));
                if ( CollectionUtils.isNotEmpty(alarmDo)) {
                    Optional<DeviceAlarmCategoryEnum> op = alarmDo.stream().filter(alarmEvent -> Objects.nonNull(DeviceAlarmCodeEnum.of(alarmEvent.getErrorCode()))).map(alarmEvent ->
                            DeviceAlarmCategoryEnum.getByValue(DeviceAlarmCodeEnum.of(alarmEvent.getErrorCode()).getCategory())).filter(Objects::nonNull).sorted(Comparator.comparingInt(Enum::ordinal)).findFirst();
                    op.ifPresent(alarmCategory -> realtimeStateDto.setAlarmState(alarmCategory.getValue()));
                }
                return realtimeStateDto;
            }).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 构建机器人分组信息的子树结构。
     */
    private static List<RobotTreeGroupBasicInfoDTO> buildGroupChild(String parentGroup, List<GroupInfoDTO> children, List<RobotRealtimeInfo> pdaRealtimeGroupList) {
        if (CollectionUtils.isEmpty(children)) {
            return new ArrayList<>();
        }
        List<RobotTreeGroupBasicInfoDTO> child = new ArrayList<>();
        Function<RobotRealtimeInfo, String> groupKey = robotRealtimeInfo ->
                new StringBuilder().append(robotRealtimeInfo.getGroupOne()).append("_").append(robotRealtimeInfo.getGroupTwo()).append("_").append(robotRealtimeInfo.getGroupThree()).toString();
        Map<String, List<RobotRealtimeInfo>> deviceMap = pdaRealtimeGroupList.stream().collect(Collectors.groupingBy(groupKey));
        for (GroupInfoDTO groupNoDto : children) {
            RobotTreeGroupBasicInfoDTO basicInfoDto = new RobotTreeGroupBasicInfoDTO();
            basicInfoDto.setGroupNo(groupNoDto.getGroupNo());
            basicInfoDto.setGroupName(groupNoDto.getName());
            String currentGroup = new StringBuilder().append(parentGroup).append("_").append(groupNoDto.getGroupNo()).toString();
            List<RobotTreeGroupBasicInfoDTO> childList = buildGroupChild(currentGroup, groupNoDto.getChildren(), pdaRealtimeGroupList);
            basicInfoDto.setChild(childList);
            basicInfoDto.setTotalCount(childList.stream().mapToInt(RobotTreeGroupBasicInfoDTO::getTotalCount).sum());
            basicInfoDto.setOnlineCount(childList.stream().mapToInt(RobotTreeGroupBasicInfoDTO::getOnlineCount).sum());
            List<RobotRealtimeInfo> deviceList = deviceMap.get(currentGroup);
            if (CollectionUtils.isEmpty(childList) && CollectionUtils.isNotEmpty(deviceList)) {
                List<RobotTreeDeviceRealtimeInfoDTO> deviceDtoList = deviceList.stream().map(device -> {
                    RobotTreeDeviceRealtimeInfoDTO realtimeInfoDto = new RobotTreeDeviceRealtimeInfoDTO();
                    realtimeInfoDto.setDeviceName(device.getDeviceName());
                    realtimeInfoDto.setRemarkName(device.getRemarkName());
                    realtimeInfoDto.setRealtimeStatus(device.getRealtimeStatus());
                    return realtimeInfoDto;
                }).collect(Collectors.toList());
                basicInfoDto.setDeviceList(deviceDtoList);
                basicInfoDto.setTotalCount(basicInfoDto.getTotalCount() + deviceDtoList.size());
                basicInfoDto.setOnlineCount(basicInfoDto.getOnlineCount() + (int)deviceDtoList.stream().filter(
                        device -> !StringUtils.equals(device.getRealtimeStatus(), DeviceRealtimeStateEnum.OFFLINE.getValue())).count());
            }
            child.add(basicInfoDto);
        }
        return child;
    }

    /**
     * 构建机器人实时信息列表。
     */
    private List<RobotDeviceRealtimePageInfoDTO> buildRobotRealtimeInfo(List<RobotRealtimeInfo> records) {
        List<RobotDeviceRealtimePageInfoDTO> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(records)) {
            return resultList;
        }
        List<String> deviceList =
                records.stream().map(RobotRealtimeInfo::getDeviceName).collect(Collectors.toList());
        List<Map<String, Object>> mapStatusResult = transportDeviceApiManager.getDeviceStatusList(deviceList, DevicePropertyCodeEnum.MONITOR_ROBOT_STATUS.getPropertyList());
        Map<String, List<RobotAlarmDO.DeviceAlarmEventDO>> alarmMap = robotAlarmRepository.listMap(deviceList);
        Map<String, RobotIssueDO> issueDOMap = robotIssueRepository.listMap(deviceList);
        Map<String, RobotScheduleDO> scheduleDoMap = robotScheduleRepository.listMap(deviceList);

        Map<String, RobotDeviceRealtimeInfoDTO> recordMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(mapStatusResult)) {
            mapStatusResult.stream().forEach(data -> {
                RobotDeviceRealtimeInfoDTO realtimeStateDto = new RobotDeviceRealtimeInfoDTO();
                BeanUtil.fillBeanWithMapIgnoreCase(data, realtimeStateDto, true);
                recordMap.put(realtimeStateDto.getDeviceName(), realtimeStateDto);
            });
        }
        return records.stream().map(record -> {
            RobotDeviceRealtimePageInfoDTO pageInfoDto = new RobotDeviceRealtimePageInfoDTO();
            pageInfoDto.setRealtimeStatus(record.getRealtimeStatus());
            pageInfoDto.setDeviceName(record.getDeviceName());
            pageInfoDto.setGroupName(record.getGroupName());
            pageInfoDto.setGroupLevelName(record.getGroupLevelName());
            pageInfoDto.setRemarkName(record.getRemarkName());
            pageInfoDto.setProductModelName(record.getProductModelName());
            if (StringUtils.isNotBlank(record.getWorkMode()) && Objects.nonNull(DeviceWorkModeEnum.of(record.getWorkMode()))) {
               pageInfoDto.setWorkModeName(DeviceWorkModeEnum.of(record.getWorkMode()).getWorkModeName());
            }
            RobotDeviceRealtimeInfoDTO realtimeInfoDto = recordMap.getOrDefault(record.getDeviceName(), new RobotDeviceRealtimeInfoDTO());
            pageInfoDto.setChargingState(Objects.equals(realtimeInfoDto.getCharging(), 1));
            pageInfoDto.setPower(Optional.ofNullable(realtimeInfoDto.getBattery()).map(battery -> Double.valueOf(battery)).orElse(0.0));
            pageInfoDto.setEmergencyState(Objects.equals(realtimeInfoDto.getEmergencyState(), 1));
            RobotScheduleDO robotScheduleDo = scheduleDoMap.get(record.getDeviceName());
             if (StringUtils.equals(record.getProductKey(), ProductTypeEnum.INSPECTOR.getValue())) {
                pageInfoDto.setTaskType(InspectTaskTypeEnum.getByValue(realtimeInfoDto.getTaskType()).getName());
            }  else if (Objects.nonNull(robotScheduleDo)) {
                pageInfoDto.setTaskType(robotScheduleDo.getTaskTypeName());
                if (Objects.nonNull(robotScheduleDo.getLatestOutTime()) && Objects.nonNull(robotScheduleDo.getReminderTime()) &&
                        DateUtil.between(new Date(), robotScheduleDo.getLatestOutTime(), DateUnit.MINUTE, false) <= robotScheduleDo.getReminderTime()) {
                    pageInfoDto.setTaskEtcTime(robotScheduleDo.getLatestOutTime());
                }
            }
            List<RobotAlarmDO.DeviceAlarmEventDO> alarmDo = alarmMap.get(record.getDeviceName());
            List<RobotDeviceAbnormalListDTO> alarmEvent = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(alarmDo)) {
                alarmEvent = alarmDo.stream().sorted(Comparator.comparing(DeviceAlarmEventDO::getReportTime))
                        .map(alarm -> {
                            RobotDeviceAbnormalListDTO abnormalDto = new RobotDeviceAbnormalListDTO();
                            abnormalDto.setAlarmCode(alarm.getErrorCode());
                            abnormalDto.setAlarmLevel(alarm.getErrorLevel());
                            DeviceAlarmCodeEnum alarmCodeEnum = DeviceAlarmCodeEnum.of(alarm.getErrorCode());
                            if (Objects.nonNull(alarmCodeEnum)) {
                                abnormalDto.setAlarmCodeName(alarmCodeEnum.getAlarmMsg());
                            }
                            AlarmLevelEnum alarmLevelEnum = AlarmLevelEnum.valueOf(alarm.getErrorLevel());
                            if (Objects.nonNull(alarmLevelEnum)) {
                                abnormalDto.setAlarmLevelName(alarmLevelEnum.getName());
                            }
                            abnormalDto.setReportTime(alarm.getReportTime());
                            return abnormalDto;
                        }).collect(Collectors.toList());
            }
            pageInfoDto.setAlarmEvent(alarmEvent);
            RobotIssueDO robotIssueDo = issueDOMap.get(record.getDeviceName());
            if(Objects.nonNull(robotIssueDo)) {
                pageInfoDto.setRemoteCall(robotIssueDo.isRemoteCall());
                pageInfoDto.setFollowUser(MapUtils.isNotEmpty(robotIssueDo.getFollowUserMap())?
                        robotIssueDo.getFollowUserMap().entrySet().iterator().next().getValue() : "");
            }
            return pageInfoDto;
        }).collect(Collectors.toList());
    }

}
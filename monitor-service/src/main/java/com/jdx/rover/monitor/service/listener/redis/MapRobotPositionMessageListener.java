package com.jdx.rover.monitor.service.listener.redis;

import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.dto.robot.RobotMapRealtimeInfoDTO;
import com.jdx.rover.monitor.dto.websocket.WsResult;
import jakarta.websocket.Session;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.listener.MessageListener;

import java.io.IOException;

/**
 * 机器人地图页实时信息更新推送
 *
 * <AUTHOR>
 * @date 2025/05/10
 */
@Slf4j
public class MapRobotPositionMessageListener implements MessageListener<String> {
  private Session session;

  public MapRobotPositionMessageListener(Session session) {
    this.session = session;
  }

  @Override
  public void onMessage(CharSequence channel, String msg) {
    WsResult<RobotMapRealtimeInfoDTO> dto = JsonUtils.readValue(msg, WsResult.class);
    if (dto == null) {
      return;
    }
    synchronized (this.session) {
      try {
        String data = JsonUtils.writeValueAsString(dto);
        log.info("推送{}机器人地图数据{}",session.getId(), data);
        this.session.getBasicRemote().sendText(data);
      } catch (IOException e) {
        log.error("Map send robot realtime update exception", e);
      }
    }
  }
}

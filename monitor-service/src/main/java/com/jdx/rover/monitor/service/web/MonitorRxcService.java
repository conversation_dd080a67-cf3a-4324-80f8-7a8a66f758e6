package com.jdx.rover.monitor.service.web;

import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.metadata.domain.vo.word.WordMonitorAddVo;
import com.jdx.rover.metadata.domain.vo.word.WordMonitorDeleteVo;
import com.jdx.rover.metadata.jsf.service.word.MetadataWordMonitorService;
import com.jdx.rover.monitor.common.utils.user.UserUtils;
import com.jdx.rover.monitor.vo.MonitorRemoteVoiceVo;
import com.jdx.rover.monitor.vo.MonitorWordAddVO;
import com.jdx.rover.monitor.vo.MonitorWordDeleteVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * This is a remote call service function.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */

@Service
public class MonitorRxcService {

    @Autowired
    private MetadataWordMonitorService metadataWordMonitorJsfService;

    /**
     * <p>
     * Get word list.
     * </p>
     */
    public HttpResult getWordList() {
        return metadataWordMonitorJsfService.getWordList();
    }

    /**
     * <p>
     * Add word to metadata.
     * </p>
     */
    public HttpResult addWord(MonitorWordAddVO wordMonitorAddVo) {
        WordMonitorAddVo wordAddVo = new WordMonitorAddVo();
        wordAddVo.setUserName(UserUtils.getAndCheckLoginUser());
        wordAddVo.setContent(wordMonitorAddVo.getContent());
        return metadataWordMonitorJsfService.addWord(wordAddVo);
    }

    /**
     * <p>
     * delete word in metadata.
     * </p>
     */
    public HttpResult deleteWord(MonitorWordDeleteVO monitorWordDeleteVO) {
        WordMonitorDeleteVo wordMonitorDeleteVo = new WordMonitorDeleteVo();
        wordMonitorDeleteVo.setId(monitorWordDeleteVO.getId());
        wordMonitorDeleteVo.setUserName(UserUtils.getAndCheckLoginUser());
        return metadataWordMonitorJsfService.deleteWord(wordMonitorDeleteVo);
    }

    /**
     * 远程调用车端语音接口
     * @param monitorRemoteVoiceVo 监控语音参数对象
     * @return HttpResult 对象，包含远程调用结果
     */
    public HttpResult<Void> remoteCall(MonitorRemoteVoiceVo monitorRemoteVoiceVo) {
        return HttpResult.success();
    }
}

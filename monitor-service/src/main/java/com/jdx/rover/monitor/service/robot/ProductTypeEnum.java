/**
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.service.robot;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * 消息类型枚举
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString
@Getter
public enum ProductTypeEnum {

    /**
     * 表示PDA类型的产品。
     */
    PDA("pda", "PDA", PdaRobotService.class),
    /**
     * 表示多合一产品。
     */
    INTEGRATE("integrate", "多合一", IntegrateRobotService.class),
    /**
     * 表示巡检产品。
     */
    INSPECTOR("inspector", "巡检", InspectorRobotService.class),
    /**
     * 表示无人车产品。
     */
    ROVER("rover", "无人车", RoverVehicleService.class),
    ;
    /**
     * <p>
     * 值
     * </p>
     */
    private final String  value;

    /**
     * <p>
     * 名称
     * </p>
     */
    private final String  name;

    /**
     * <p>
     * 执行类
     * </p>
     */
    private final Class clazz;

    /**
     * 根据给定的值获取对应的 ProductTypeEnum 枚举类型。
     */
    public static ProductTypeEnum getByValue(String value) {
        for (ProductTypeEnum em : ProductTypeEnum.values()) {
            if (Objects.equals(value, em.getValue())) {
                return em;
            }
        }
        return null;
    }

}

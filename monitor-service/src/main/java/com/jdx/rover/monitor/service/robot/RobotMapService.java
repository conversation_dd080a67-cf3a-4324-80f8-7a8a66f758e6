/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.service.robot;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jdx.k2.indoor.map.jsf.domain.dto.mapInfo.MapInfoDTO;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.device.jsfapi.domain.dto.group.GroupInfoDetailDTO;
import com.jdx.rover.device.jsfapi.domain.dto.tag.TagInfoDTO;
import com.jdx.rover.device.jsfapi.domain.vo.group.GroupVO;
import com.jdx.rover.device.jsfapi.service.server.group.IntelligentDeviceServerGroupService;
import com.jdx.rover.monitor.api.domain.web.jsf.vo.robot.RobotDeviceBasicSearchVO;
import com.jdx.rover.monitor.common.utils.jts.GeometryUtils;
import com.jdx.rover.monitor.dto.robot.RobotDeviceRealtimeInfoDTO;
import com.jdx.rover.monitor.dto.robot.RobotGroupMapInfoDTO;
import com.jdx.rover.monitor.dto.robot.RobotMapRouteInfoDTO;
import com.jdx.rover.monitor.entity.device.RobotScheduleDO;
import com.jdx.rover.monitor.entity.device.RobotTaskRouteInfoDO;
import com.jdx.rover.monitor.enums.GroupTagEnum;
import com.jdx.rover.monitor.enums.MonitorErrorEnum;
import com.jdx.rover.monitor.enums.device.DevicePropertyCodeEnum;
import com.jdx.rover.monitor.manager.device.IntelligentDeviceGroupApiManager;
import com.jdx.rover.monitor.manager.device.TransportDeviceApiManager;
import com.jdx.rover.monitor.manager.device.indoormap.IndoorMapApiManager;
import com.jdx.rover.monitor.manager.robot.RobotRealtimeInfoManager;
import com.jdx.rover.monitor.po.robot.RobotRealtimeInfo;
import com.jdx.rover.monitor.repository.redis.robot.RobotScheduleRepository;
import com.jdx.rover.monitor.repository.redis.robot.RobotTaskRouteRepository;
import com.jdx.rover.monitor.vo.robot.RobotMapInfoRequestVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.geotools.geojson.geom.GeometryJSON;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.LineString;
import org.springframework.stereotype.Service;

import java.io.StringWriter;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 机器人地图服务
 *
 * <AUTHOR>
 * @date 2025/05/10
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RobotMapService {

    /**
     * 指令服务
     */
    private final RobotRealtimeInfoManager robotRealtimeInfoManager;

    /**
     * 设备服务
     */
    private final TransportDeviceApiManager transportDeviceApiManager;

    /**
     * 提供室内地图数据访问和管理的API服务。
     */
    private final IndoorMapApiManager indoorMapApiManager;

    /**
     * 提供机器人调度相关的数据访问服务。
     */
    private final RobotScheduleRepository robotScheduleRepository;

    /**
     * 提供机器人任务路线相关的数据访问服务。
     */
    private final RobotTaskRouteRepository robotTaskRouteRepository;

    /**
     * 提供智能设备服务器组相关的服务功能
     */
    private final IntelligentDeviceGroupApiManager intelligentDeviceGroupApiManager;

    /**
     * 根据产品和分组获取地图信息
     * @param mapInfoRequestVo 机器人地图请求
     * @return 发送结果
     */
    public HttpResult<RobotGroupMapInfoDTO> getMapInfoByGroup(RobotMapInfoRequestVO mapInfoRequestVo) {
        if (Objects.isNull(mapInfoRequestVo)) {
            return HttpResult.error(MonitorErrorEnum.ERROR_CALL_CHECK_PARAM.getCode(), MonitorErrorEnum.ERROR_CALL_CHECK_PARAM.getMessage());
        }
        RobotGroupMapInfoDTO groupMapInfoDto = new RobotGroupMapInfoDTO();
        LambdaQueryWrapper<RobotRealtimeInfo> queryWrapper = new LambdaQueryWrapper<RobotRealtimeInfo>();
        queryWrapper.eq(StringUtils.isNotEmpty(mapInfoRequestVo.getGroupOne()), RobotRealtimeInfo::getGroupOne, mapInfoRequestVo.getGroupOne());
        queryWrapper.eq(StringUtils.isNotEmpty(mapInfoRequestVo.getGroupTwo()), RobotRealtimeInfo::getGroupTwo, mapInfoRequestVo.getGroupTwo());
        queryWrapper.eq(StringUtils.isNotEmpty(mapInfoRequestVo.getProductKey()), RobotRealtimeInfo::getProductKey, mapInfoRequestVo.getProductKey());
        queryWrapper.in(CollectionUtils.isNotEmpty(mapInfoRequestVo.getWorkMode()), RobotRealtimeInfo::getWorkMode, mapInfoRequestVo.getWorkMode());
        queryWrapper.select(RobotRealtimeInfo::getDeviceName,RobotRealtimeInfo::getProductKey);
        List<RobotRealtimeInfo> robotRealtimeInfoList = robotRealtimeInfoManager.list(queryWrapper);
        log.info("获取机器人列表数据为{}", JsonUtils.writeValueAsString(robotRealtimeInfoList));
        if (CollectionUtils.isEmpty(robotRealtimeInfoList)) {
            return HttpResult.success(groupMapInfoDto);
        }
        List<String> vehicleNameList = robotRealtimeInfoList.stream().map(RobotRealtimeInfo::getDeviceName).collect(Collectors.toList());
        List<Map<String, Object>> realtimeMap = transportDeviceApiManager.getDeviceStatusList(vehicleNameList, DevicePropertyCodeEnum.MONITOR_ROBOT_VERSION.getPropertyList());
        if (CollectionUtils.isEmpty(realtimeMap)) {
            return HttpResult.success(groupMapInfoDto);
        }
        Optional<RobotDeviceRealtimeInfoDTO> newMapVersion = realtimeMap.stream().map(dataMap -> {
            RobotDeviceRealtimeInfoDTO realtimeStateDto = new RobotDeviceRealtimeInfoDTO();
            BeanUtil.fillBeanWithMapIgnoreCase(dataMap, realtimeStateDto, true);
            return realtimeStateDto;
        }).filter(realtimeStatus -> !StringUtils.isAnyBlank(realtimeStatus.getMapId(), realtimeStatus.getMapVersion()))
                .sorted(Comparator.comparing(RobotDeviceRealtimeInfoDTO::getMapVersion).reversed()).findFirst();
        if (newMapVersion.isPresent()) {
            groupMapInfoDto.setMapId(newMapVersion.get().getMapId());
            groupMapInfoDto.setMapVersion(newMapVersion.get().getMapVersion());
        }
        return HttpResult.success(groupMapInfoDto);
    }

    /**
     * 根据分组编号获取机器人分组地图信息
     * @param productKey 产品标识
     * @param groupNo 分组编号
     * @return 包含机器人分组地图信息的HTTP响应结果
     */
    public RobotGroupMapInfoDTO getMapInfoByGroupNo(String productKey, String groupNo) {
        // 根据分组，获取分组标签
        GroupInfoDetailDTO groupInfoDetail = intelligentDeviceGroupApiManager.getGroupInfoDetail(groupNo);
        if (Objects.isNull(groupInfoDetail) || CollectionUtils.isEmpty(groupInfoDetail.getTagDTOList())) {
            return new RobotGroupMapInfoDTO();
        }
        Map<String, String> tagMap = groupInfoDetail.getTagDTOList().stream().collect(Collectors.toMap(TagInfoDTO::getKey, TagInfoDTO::getValue));
        String distributeNo = tagMap.get(GroupTagEnum.DISTRIBUTE_NO.getValue());
        String warehouseNo = tagMap.get(GroupTagEnum.WAREHOUSE_NO.getValue());
        String areaNo = tagMap.get(GroupTagEnum.AREA_NO.getValue());
        RobotGroupMapInfoDTO result = new RobotGroupMapInfoDTO();
        MapInfoDTO mapInfoDto = indoorMapApiManager.getMapInfoByWareHouse(String.join("-", distributeNo, warehouseNo, areaNo));
        if (Objects.nonNull(mapInfoDto)) {
            result.setMapId(mapInfoDto.getNumber());
            result.setMapVersion(mapInfoDto.getVersion());
        }
        return result;
    }

    /**
     * 获取机器人设备的路径信息。
     * @param robotDeviceBasicSearchVo 机器人基本信息
     * @return 如果机器人设备存在任务路线
     */
    public RobotMapRouteInfoDTO getMapDeviceRoute(RobotDeviceBasicSearchVO robotDeviceBasicSearchVo) {
        RobotScheduleDO robotScheduleDo = robotScheduleRepository.get(robotDeviceBasicSearchVo.getDeviceName());
        if (Objects.isNull(robotScheduleDo)) {
            return new RobotMapRouteInfoDTO();
        }
        RobotTaskRouteInfoDO robotMapRouteInfoDo = robotTaskRouteRepository.get(robotDeviceBasicSearchVo.getDeviceName());
        RobotMapRouteInfoDTO robotMapRouteInfo = new RobotMapRouteInfoDTO();
        if (Objects.nonNull(robotScheduleDo.getParkStop())) {
            robotMapRouteInfo.setStopId(robotScheduleDo.getParkStop().getStopId());
            robotMapRouteInfo.setStopName(robotScheduleDo.getParkStop().getStopName());
            robotMapRouteInfo.setX(robotScheduleDo.getParkStop().getX());
            robotMapRouteInfo.setY(robotScheduleDo.getParkStop().getY());
        }
        if (Objects.nonNull(robotMapRouteInfoDo) && CollectionUtils.isNotEmpty(robotMapRouteInfoDo.getRoutePoint())) {
            LineString lineString = GeometryUtils.createLineString(robotMapRouteInfoDo.getRoutePoint().stream().map(point -> {
                Coordinate coordinate = new Coordinate();
                coordinate.setX(point.getX());
                coordinate.setY(point.getY());
                return coordinate;
            }).collect(Collectors.toList()));
            try {
                // 使用GeoTools的GeometryJSON将Point转换为GeoJSON
                StringWriter writer = new StringWriter();
                GeometryJSON geometryJSON = new GeometryJSON(8); // 8位小数精度
                geometryJSON.write(lineString, writer);
                robotMapRouteInfo.setPlanRoute(writer.toString());
            }catch (Exception e) {
                log.error("获取机器人设备的路径信息失败", e);
            }
        }
        return robotMapRouteInfo;
    }
}

/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.service.datacollection;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.api.domain.enums.MonitorTrackingEventEnum;
import com.jdx.rover.monitor.common.utils.jts.GeometryUtils;
import com.jdx.rover.monitor.common.utils.jts.WktUtils;
import com.jdx.rover.monitor.entity.datacollection.DataCollectionVehicleSceneDO;
import com.jdx.rover.monitor.enums.datacollection.DataCollectionSceneStatusEnum;
import com.jdx.rover.monitor.enums.redis.RedisTopicEnum;
import com.jdx.rover.monitor.enums.websocket.WebsocketEventTypeEnum;
import com.jdx.rover.monitor.manager.datacollection.DataCollectionSceneManager;
import com.jdx.rover.monitor.manager.datacollection.DataCollectionSceneTagManager;
import com.jdx.rover.monitor.manager.datacollection.DataCollectionTagManager;
import com.jdx.rover.monitor.manager.hanlp.HanlpManager;
import com.jdx.rover.monitor.manager.shadow.ShadowDataCollectionManager;
import com.jdx.rover.monitor.manager.shadow.ShadowVehicleRouteManager;
import com.jdx.rover.monitor.po.datacollection.DataCollectionScene;
import com.jdx.rover.monitor.po.datacollection.DataCollectionSceneTag;
import com.jdx.rover.monitor.po.datacollection.DataCollectionTag;
import com.jdx.rover.monitor.repository.redis.NumberCacheRepository;
import com.jdx.rover.monitor.repository.redis.RedissonUtils;
import com.jdx.rover.monitor.repository.redis.datacollection.DataCollectionVehicleSceneRepository;
import com.jdx.rover.monitor.service.config.ducc.DuccConfigProperties;
import com.jdx.rover.monitor.service.config.ducc.DuccMobileProperties;
import com.jdx.rover.monitor.vo.datacollection.ws.DataCollectionTaskVehicleTopicMsg;
import com.jdx.rover.shadow.api.domain.dto.ShadowVehicleRouteInfoDTO;
import com.jdx.rover.shadow.api.domain.vo.ShadowDataCollectionSceneAddVO;
import com.jdx.rover.shadow.api.domain.vo.ShadowVehicleRouteVO;
import io.netty.util.HashedWheelTimer;
import io.netty.util.Timeout;
import io.netty.util.TimerTask;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RTopic;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 数采车场景接口
 *
 * <AUTHOR>
 * @date 2025/07/15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataCollectionSceneService {

    /**
     * 管理数据采集场景的增删改查等操作
     */
    private final DataCollectionSceneManager dataCollectionSceneManager;

    /**
     * 管理数据采集标签的增删改查等操作
     */
    private final DataCollectionTagManager dataCollectionTagManager;

    /**
     * 管理数据采集场景与标签关联关系的增删改查等操作
     */
    private final DataCollectionSceneTagManager dataCollectionSceneTagManager;

    /**
     * 提供自然语言处理功能，用于处理与数据采集场景相关的文本分析任务
     */
    private final HanlpManager hanlpManager;

    /**
     * 管理影子车辆路线信息，用于处理与数据采集车行驶路线相关的操作
     */
    private final ShadowVehicleRouteManager vehicleRouteManager;

    /**
     * 管理影子场景信息，用于处理与数据采集车行驶路线相关的操作
     */
    private final ShadowDataCollectionManager shadowDataCollectionManager;

    /**
     * 管理数据采集车与场景关联关系的数据访问操作
     */
    private final DataCollectionVehicleSceneRepository vehicleSceneRepository;

    /**
     * 存储DUCC(数据采集配置中心)的相关配置属性
     */
    private final DuccConfigProperties duccConfigProperties;

    /**
     * 存储DUCC(数据采集配置中心)的相关配置属性
     */
    private final DuccMobileProperties duccMobileProperties;

    /**
     * 管理数字缓存的存储与访问操作
     */
    private final NumberCacheRepository numberCacheRepository;

    /*
     * 时间轮, 槽位数32个，每个10S,5min 保障在1圈以内
     */
    private static final HashedWheelTimer wheelTimer = new HashedWheelTimer(10, TimeUnit.SECONDS, 20);

    /**
     * 1、新增数据采集场景
     */
    @Transactional(rollbackFor = Exception.class)
    public DataCollectionVehicleSceneDO addDataCollectionScene(String vehicleName, Date reportTime) {
        // 接收到的拨杆事件，判断库中是否包含在两分钟内，有则不处理，没有则新增
        DataCollectionVehicleSceneDO vehicleSceneDo = vehicleSceneRepository.get(vehicleName);
        // 1、创建新的采集任务
        DataCollectionScene dataCollectionScene = new DataCollectionScene();
        dataCollectionScene.setVehicleName(vehicleName);
        Date startTime = DateUtil.offsetSecond(reportTime, -1* duccConfigProperties.getDataCollectionLevelTimeBefore()).toJdkDate();
        Date endTime = DateUtil.offsetSecond(reportTime, duccConfigProperties.getDataCollectionLevelTimeAfter()).toJdkDate();
        dataCollectionScene.setStartTime(startTime);
        dataCollectionScene.setEndTime(endTime);
        dataCollectionScene.setReportTime(reportTime);
        dataCollectionScene.setCreateTime(new Date());
        String sceneNo = getSceneNo();
        dataCollectionScene.setSceneNumber(StringUtils.join(vehicleName, sceneNo));
        dataCollectionScene.setStatus(DataCollectionSceneStatusEnum.STANDBY.getValue());
        dataCollectionSceneManager.save(dataCollectionScene);
        vehicleSceneDo = new DataCollectionVehicleSceneDO();
        vehicleSceneDo.setVehicleName(vehicleName);
        vehicleSceneDo.setSceneNumber(dataCollectionScene.getSceneNumber());
        vehicleSceneDo.setReportTime(reportTime);
        vehicleSceneDo.setStartTime(startTime);
        vehicleSceneDo.setEndTime(endTime);
        vehicleSceneRepository.set(vehicleName, vehicleSceneDo);
        // 事务提交之后，再去拉取路径
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                wheelTimer.newTimeout(new TimerTask() {
                    @Override
                    public void run(Timeout timeout) throws Exception {
                        saveSceneDrivePath(dataCollectionScene.getId(), vehicleName, reportTime, startTime, endTime);
                    }
                }, 2, TimeUnit.MINUTES);
                sendLeverTopic(vehicleName, dataCollectionScene.getId(), reportTime);
            }
        });
        return vehicleSceneDo;
    }

    /**
     * 2、关联场景标签
     * @param sceneId 场景ID
     * @param voice 语音标识
     */
    @Transactional(rollbackFor = Exception.class)
    public void associateSceneTag(Integer sceneId, String voice) {
        // 1、提取语音中的关键词
        List<DataCollectionTag> dataCollectionTags = dataCollectionTagManager.listAllTags();
        List<String> keyWords = hanlpManager.extractKeywords(voice, dataCollectionTags.stream().map(tag -> tag.getTagName()).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(keyWords)) {
            return;
        }
        // 2、获取关联的标签
        List<DataCollectionTag> associateTagList = filterByOverlap(dataCollectionTags, keyWords, duccMobileProperties.getDataCollectionOverlapValue());
        List<String> mainKeyWords = filterKeyByOverlap(keyWords, dataCollectionTags.stream().map(DataCollectionTag::getTagName).collect(Collectors.toList()), duccMobileProperties.getDataCollectionOverlapValue());
        // 3、更新场景
        DataCollectionScene dataCollectionScene = new DataCollectionScene();
        dataCollectionScene.setId(sceneId);
        dataCollectionScene.setAudioRecognitionKeyWords(StringUtils.join(mainKeyWords, ";"));
        dataCollectionScene.setAudioRecognitionResult(voice);
        dataCollectionSceneManager.updateById(dataCollectionScene);
        if (CollectionUtils.isEmpty(associateTagList)) {
            return;
        }
        // 4、增加场景和标签的关联
        List<DataCollectionSceneTag> dataCollectionSceneTagList = associateTagList.stream().map(tag -> {
            DataCollectionSceneTag dataCollectionSceneTag = new DataCollectionSceneTag();
            dataCollectionSceneTag.setSceneId(sceneId);
            dataCollectionSceneTag.setTagId(tag.getId());
            dataCollectionSceneTag.setTagName(tag.getTagName());
            dataCollectionSceneTag.setCreateTime(new Date());
            return dataCollectionSceneTag;
        }).collect(Collectors.toList());
        dataCollectionSceneTagManager.saveBatch(dataCollectionSceneTagList);
    }

    /**
     * 提取标签相似度大于阈值的数据
     * @param tags 待筛选标签集合
     * @param keywords 关键词列表
     * @param threshold 相似度阈值（默认30）
     */
    private List<DataCollectionTag> filterByOverlap(List<DataCollectionTag> tags, List<String> keywords, int threshold) {
        if (CollectionUtils.isEmpty(tags) || CollectionUtils.isEmpty(keywords)) {
            return Collections.emptyList();
        }
        return tags.stream()
                .filter(tag -> {
                    try {
                        return hanlpManager.countWords(Lists.newArrayList(tag.getTagName()), keywords) > threshold;
                    } catch (Exception e) {
                        log.error("计算相似度失败: {}", tag.getTagName(), e);
                        return false;
                    }
                }).collect(Collectors.toList());
    }

    /**
     * 根据关键词和标签名的重叠度过滤关键词。
     * @param keywords 关键词列表
     * @param tagNames 标签名列表
     * @param threshold 重叠度阈值
     * @return 满足重叠度阈值的关键词列表
     */
    private List<String> filterKeyByOverlap(List<String> keywords, List<String> tagNames, int threshold) {
        if (CollectionUtils.isEmpty(keywords)) {
            return Collections.emptyList();
        }
        return keywords.stream()
                .filter(tag -> {
                    try {
                        return hanlpManager.countWords(Lists.newArrayList(keywords), tagNames) > threshold;
                    } catch (Exception e) {
                        log.error("计算相似度失败: {}", tag, e);
                        return false;
                    }
                }).collect(Collectors.toList());
    }

    /**
     * 保存场景的车辆行驶路径信息
     * @param sceneId 场景ID
     * @param vehicleName 车辆名称
     * @param startTime 路径开始时间
     * @param endTime 路径结束时间
     */
    private void saveSceneDrivePath(Integer sceneId, String vehicleName, Date reportTime, Date startTime, Date endTime) {
        ShadowVehicleRouteVO shadowVehicleRouteVo = new ShadowVehicleRouteVO();
        shadowVehicleRouteVo.setVehicleName(vehicleName);
        shadowVehicleRouteVo.setStartTime(startTime);
        shadowVehicleRouteVo.setEndTime(endTime);
        ShadowDataCollectionSceneAddVO shadowDataCollectionSceneAddVo = new ShadowDataCollectionSceneAddVO();
        shadowDataCollectionSceneAddVo.setSceneId(sceneId);
        shadowDataCollectionSceneAddVo.setVehicleName(vehicleName);
        shadowDataCollectionSceneAddVo.setStartTime(startTime);
        shadowDataCollectionSceneAddVo.setEndTime(endTime);
        shadowDataCollectionSceneAddVo.setReportTime(reportTime);
        shadowDataCollectionSceneAddVo.setEventNo(MonitorTrackingEventEnum.DATA_COLLECTION_SCENE_REPORT.getCode());
        shadowDataCollectionManager.addTrackingSceneEvent(shadowDataCollectionSceneAddVo);
        ShadowVehicleRouteInfoDTO routeInfoDto = vehicleRouteManager.getVehicleHistoryRoute(shadowVehicleRouteVo);
        if (Objects.isNull(routeInfoDto) || StringUtils.isBlank(routeInfoDto.getPath())) {
            return;
        }
        DataCollectionScene dataCollectionScene = new DataCollectionScene();
        dataCollectionScene.setId(sceneId);
        try {
            dataCollectionScene.setDrivePath(GeometryUtils.getGeoJsonStr(String.valueOf(sceneId), WktUtils.readWkt(routeInfoDto.getPath()), new HashMap<>()));
        } catch (Exception e) {
           log.error("获取场景行驶路径失败: {}", sceneId, e);
        }
        dataCollectionSceneManager.updateById(dataCollectionScene);
    }

    /**
     * 发送场景数据采集拨杆消息
     * @param vehicleName 车辆名称
     * @param sceneId 场景id
     * @param reportTime 上报时间
     */
    private void sendLeverTopic(String vehicleName, Integer sceneId, Date reportTime) {
        try {
            String topicName = RedisTopicEnum.DATA_COLLECTION_TASK_VEHICLE.getValue() + vehicleName;
            RTopic rTopic = RedissonUtils.getRTopic(topicName);
            DataCollectionTaskVehicleTopicMsg dataCollectionTaskVehicleTopicMsg = new DataCollectionTaskVehicleTopicMsg();
            dataCollectionTaskVehicleTopicMsg.setMessageType(WebsocketEventTypeEnum.DATA_COLLECTION_LEVER.getValue());
            dataCollectionTaskVehicleTopicMsg.setLeverTime(reportTime);
            dataCollectionTaskVehicleTopicMsg.setSceneId(sceneId);
            String jsonUrl = JsonUtils.writeValueAsString(dataCollectionTaskVehicleTopicMsg);
            rTopic.publish(jsonUrl);
            log.info("[数采车]发送场景数据采集拨杆消息, topic={}, msg={}", topicName, jsonUrl);
        } catch (Exception e) {
            log.error("[数采车]发送场景数据采集拨杆消息失败, vehicleName={}, sceneId={}", vehicleName, sceneId, e);
        }
    }

    /**
     * 生成场景号。
     * @return 生成的场景号，格式为"yyyyMMddHHmmSSSS"，其中最后四位为随机数。
     */
    private String getSceneNo() {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmm");
        String timestamp = now.format(formatter);
        // 2. 生成4位随机数字
        Random random = new Random();
        int randomNum = random.nextInt(10000); // 0~9999
        String randomCode = String.format("%04d", randomNum); // 补零至4位
        return StringUtils.join(timestamp, randomCode);
    }

}
package com.jdx.rover.monitor.vo.mobile.message;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 维修单操作传输对象
 */
@Data
public class RepairOperateVO {

    /**
     * 卡片ID
     */
    @NotNull(message = "卡片id不能为空")
    private Integer messageId;

    /**
     * 维修单号
     */
    @NotBlank(message = "维修单号不能为空")
    private String repairNumber;

    /**
     * 操作类型
     */
    @NotBlank(message = "操作类型不能为空")
    private String operateType;

    /**
     * 驳回原因
     */
    private String rejectReason;
}

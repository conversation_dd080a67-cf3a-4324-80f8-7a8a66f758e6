/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import lombok.Data;

/**
 * <p>
 * This is a stop map information data transform object entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorStopMapInfoDTO {

  /**
   * <p>
   * Represents the id of stop. The default value is null. It's changeable.
   * </p>
   */
  private Integer id;

  /**
   * <p>
   * Represents the id of goal. The default value is null. It's changeable.
   * </p>
   */
  private Integer goalId;

  /**
   * <p>
   * Represents the name of stop. The default value is null. It's changeable.
   * </p>
   */
  private String name;

  /**
   * <p>
   * Represents the type of stop. The default value is null. It's changeable.
   * </p>
   */
  @Deprecated
  private String type;

  /**
   * <p>
   * Represents the action of stop. The default value is null. It's changeable.
   * </p>
   */
  private String stopAction;

  /**
   * <p>
   * Represents the lat of stop. The default value is null. It's changeable.
   * </p>
   */
  private Double lat;

  /**
   * <p>
   * Represents the lon of stop. The default value is null. It's changeable.
   * </p>
   */
  private Double lon;

    /**
     * 停留状态(INIT,START,ARRIVED,DEPART,SKIP,STAY)
     * @see com.jdx.rover.schedule.api.domain.enums.StopTravelStatus
     */
    private String travelStatus;
}

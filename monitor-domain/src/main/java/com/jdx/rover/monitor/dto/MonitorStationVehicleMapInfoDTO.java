/***************************************************************************
 *
 * Copyright (c) 2021 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.dto;

import java.util.List;

import com.jdx.rover.monitor.entity.MonitorRoutingPointEntity;

import lombok.Data;

/**
 * <p>
 * This is a stop map information data transform object entity.
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class MonitorStationVehicleMapInfoDTO {

  /**
   * <p>
   * Represents the name of vehicle. The default value is null. It's changeable.
   * </p>
   */
  private String name;
  
  /**
   * <p>
   * Represents the bussiness type of vehicle. The default value is null. It's
   * changeable.
   * </p>
   */
  private String businessType;

  /**
   * <p>
   * Represents the lat of vehicle. The default value is null. It's changeable.
   * </p>
   */
  private Double lat;

  /**
   * <p>
   * Represents the lon of vehicle. The default value is null. It's changeable.
   * </p>
   */
  private Double lon;

  /**
   * <p>
   * Represents the stop map info. The default value is null. It's changeable.
   * </p>
   */
  private List<MonitorStopMapInfoDTO> stop;

  /**
   * <p>
   * Represents the planning routing point. The default value is null. It's changeable.
   * </p>
   */
  private List<MonitorRoutingPointEntity> planningRoutingPoint;

  /**
   * <p>
   * Represents the finished routing point. The default value is null. It's changeable.
   * </p>
   */
  private List<MonitorRoutingPointEntity> finishedRoutingPoint;


    /**
     * 当前线路停靠点
     */
    private List<MonitorStopMapInfoDTO> currentStop;

    /**
     * 当前段线路点位
     */
    private List<MonitorRoutingPointEntity> currentRoutingPoint;

    /**
     * 当前线路已行走点位
     */
    private List<MonitorRoutingPointEntity> currentFinishedRoutingPoint;
}

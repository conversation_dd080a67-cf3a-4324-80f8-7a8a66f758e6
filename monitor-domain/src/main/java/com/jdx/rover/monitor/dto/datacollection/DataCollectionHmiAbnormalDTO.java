/*
 * Copyright (c) 2022 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.datacollection;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 推送HMI告警信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/04/10
 */
@Data
public class DataCollectionHmiAbnormalDTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 当前机器人HMI的告警信息列表。
   */
  private List<AbnormalInfo> currentAlarm;

  /**
   * 告警详情。
   */
  @Data
  public static class AbnormalInfo {
    /**
     * 告警编号
     */
    private String alarmNumber;

    /**
     * 告警错误描述
     */
    private String errorCode;

    /**
     * 触发时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date timestamp;

    /**
     * 告警类型
     */
    private String alarmTypeName;

    /**
     * 解决方案
     */
    private String solutionTypeName;

    /**
     * 告警的详细描述信息
     */
    private String description;

    /**
     * 告警所属类别
     */
    private String category;
  }

}
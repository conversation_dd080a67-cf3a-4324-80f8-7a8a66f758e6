/***************************************************************************
 *
 * Copyright (c) 2022 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>
 * 分组标签映射字段
 * </p>
 * 
 * 
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor
@ToString
public enum GroupTagEnum {
  /**
   * <p>
   * The enumerate type.
   * </p>
   */
  WAREHOUSE_NO("warehouseNo", "仓库编号"),
  DISTRIBUTE_NO("distributeNo", "配送中心号"),
  AREA_NO("areaNo", "地图跨编号"),

  ;

  /**
   * 表示分组标签的类型标识符。
   */
  @Getter
  @Setter
  private String value;

  /**
   * 表示分组标签的中文名称。
   */
  @Getter
  @Setter
  private String name;
}

/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.dto.datacollection;

import lombok.Data;

/**
 * 数据采集需求详情标签 DTO
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
public class DataCollectionRequirementDetailDataTag {

    /**
     * 标签ID
     */
    private Integer tagId;

    /**
     * 父级标签ID
     */
    private Integer parentId;

    /**
     * 父级标签Index
     */
    private Integer parentIndex;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 标签类型（MAIN：一级标签，CHILDREN：二级标签，DETAIL：三级标签）
     * @see com.jdx.rover.monitor.enums.datacollection.DataCollectionTagTypeEnum
     */
    private String tagType;

    /**
     * 标签需求数量
     */
    private Integer count;

    /**
     * 标签是否启用
     */
    private Boolean enabled;
}

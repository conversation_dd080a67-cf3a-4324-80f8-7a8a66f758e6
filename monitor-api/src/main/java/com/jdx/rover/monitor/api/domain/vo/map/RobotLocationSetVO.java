package com.jdx.rover.monitor.api.domain.vo.map;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: 设置机器人定位请求实体
 * @date 2025年05月22日
 * @version: 1.0
 */
@Data
public class RobotLocationSetVO {

    /**
     * 车架号
     */
    @NotBlank(message = "车架号不能为空")
    private String robotSn;

    /**
     * 地图id
     */
    private String mapId;

    /**
     * 地图版本
     */
    private String mapVersion;

    /**
     * 坐标X值
     */
    @NotNull(message = "坐标X值不能为空")
    private Double x;

    /**
     * 坐标Y值
     */
    @NotNull(message = "坐标Y值不能为空")
    private Double y;

    /**
     * 角度
     */
    @NotNull(message = "角度不能为空")
    private Double yaw;
}

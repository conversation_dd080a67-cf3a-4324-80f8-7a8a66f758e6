/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.monitor.api.domain.web.jsf.vo.robot;

import com.jdx.rover.common.domain.page.PageVO;
import lombok.Data;

import java.util.List;

/**
 * 机器人分页查询请求
 *
 * <AUTHOR>
 */
@Data
public class RobotDevicePageSearchVO extends PageVO {

  /**
   * <p>
   * 产品标识
   * </p>
   */
  private String productKey;

  /**
   * 设备状态
   */
  private String realtimeStatus;

  /**
   * 一级分组
   */
  private String groupOne;

  /**
   * 二级分组
   */
  private String groupTwo;

  /**
   * 三级分组
   */
  private String groupThree;

  /**
   * 工作模式
   */
  private List<String> workMode;


}

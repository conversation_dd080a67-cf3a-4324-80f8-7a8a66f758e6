/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.api.domain.web.jsf.dto.robot;

import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 多合一设备实时状态信息
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/25
 */
@Data
public class RobotDeviceRealtimeStateDTO {
    /**
     * <p>
     * 产品标识
     * </p>
     */
    private String productKey;

    /**
     * 设备编号
     */
    private String deviceNo;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * <p>
     * 实时状态(在线/离线/异常)
     * </p>
     */
    private String realtimeStatus;

    /**
     * <p>
     * 实时状态名(在线/离线/异常)
     * </p>
     */
    private String realtimeStatusName;

    /**
     * <p>
     * 是否充电
     * </p>
     */
    private Boolean chargingState;

    /**
     * <p>
     * 电量
     * </p>
     */
    private Double power;

    /**
     * <p>
     * 定位置信度（小于0.4低-红色，0.4-0.55正常-白色，0.55高-绿色。）
     * </p>
     */
    private Double sceneSignal;

    /**
     * <p>
     * 坐标
     * </p>
     */
    private Double x;

    /**
     * <p>
     * 坐标
     * </p>
     */
    private Double y;

    /**
     * <p>
     * yaw坐标
     * </p>
     */
    private Double yaw;

    /**
     * <p>
     * 急停状态
     * </p>
     */
    private Boolean emergencyState;

    /**
     * <p>
     * 电机使能
     * </p>
     */
    private Boolean motorEnabled;

    /**
     * <p>
     * 速度
     * </p>
     */
    private Double speed;

    /**
     * <p>
     * 波次任务完成时间
     * </p>
     */
    private Date taskEtcTime;

    /**
     * <p>
     * 上装类型名称
     * </p>
     */
    private String shelfTypeName;

    /**
     * <p>
     * 上装编号
     * </p>
     */
    private String shelfNo;

    /**
     * <p>
     * 接管状态
     * </p>
     */
    private Boolean takeOverState;

    /**
     * <p>
     * 接管用户
     * </p>
     */
    private String takeOverUser;

    /**
     * <p>
     * 工作模式
     * </p>
     */
    private String workModeName;

    /**
     * <p>
     * 任务类型
     * </p>
     */
    private String taskType;

}
